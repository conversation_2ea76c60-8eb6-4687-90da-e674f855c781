# NYC Taxi Analytics - Simple Medallion Architecture

A simplified implementation of Medallion Architecture (Bronze, Silver, Gold) for NYC Taxi data using Jupyter notebooks.

## Project Structure

```
simple_version/
├── README.md
├── 01_bronze_layer.ipynb      # Raw data ingestion
├── 02_silver_layer.ipynb      # Data cleaning and validation
├── 03_gold_layer.ipynb        # Business analytics and aggregations
└── config.py                  # Simple configuration
```

## Data Schema

The source table `nyctaxi.trips` contains the following columns:
- `tpep_pickup_datetime`: Pickup timestamp
- `tpep_dropoff_datetime`: Dropoff timestamp  
- `trip_distance`: Trip distance in miles
- `fare_amount`: Fare amount in dollars
- `pickup_zip`: Pickup ZIP code
- `dropoff_zip`: Dropoff ZIP code

## Architecture Layers

### Bronze Layer (01_bronze_layer.ipynb)
- Raw data ingestion from `nyctaxi.trips`
- Minimal transformations
- Add audit columns (ingestion timestamp, etc.)
- Save as Delta table

### Silver Layer (02_silver_layer.ipynb)
- Data quality validation
- Clean and standardize data
- Add derived columns (trip duration, speed, etc.)
- Remove duplicates and invalid records

### Gold Layer (03_gold_layer.ipynb)
- Business aggregations and analytics
- Daily trip summaries
- ZIP code analysis
- Time-based patterns
- Revenue analytics

## Getting Started

1. Open Databricks workspace
2. Upload the notebooks to your workspace
3. Update `config.py` with your database details
4. Run notebooks in sequence: Bronze → Silver → Gold

## Requirements

- Azure Databricks
- Access to `nyctaxi.trips` sample database
- PySpark/Delta Lake (pre-installed in Databricks)
