# Simple Configuration for NYC Taxi Analytics

# Source Configuration
SOURCE_DATABASE = "nyctaxi"
SOURCE_TABLE = "trips"

# Target Database
TARGET_CATALOG = 'deltacatalog'
TARGET_DATABASE = "nyc_taxi_analytics"

# Table Names
BRONZE_TABLE = f"{TARGET_CATALOG}.{TARGET_DATABASE}.bronze_trips"
SILVER_TABLE = f"{TARGET_CATALOG}.{TARGET_DATABASE}.silver_trips"

# Gold Layer Tables
GOLD_DAILY_SUMMARY = f"{TARGET_CATALOG}.{TARGET_DATABASE}.gold_daily_summary"
GOLD_ZIP_ANALYSIS = f"{TARGET_CATALOG}.{TARGET_DATABASE}.gold_zip_analysis"
GOLD_TIME_ANALYSIS = f"{TARGET_CATALOG}.{TARGET_DATABASE}.gold_time_analysis"

# Data Quality Thresholds
MIN_TRIP_DISTANCE = 0.1
MAX_TRIP_DISTANCE = 100
MIN_FARE_AMOUNT = 0
MAX_FARE_AMOUNT = 500
