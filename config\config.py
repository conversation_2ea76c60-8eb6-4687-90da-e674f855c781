# Configuration file for NYC Taxi Analytics Pipeline

# Database and Table Configuration
SOURCE_DATABASE = "nyctaxi"
SOURCE_TABLE = "trips"

# Target Database Configuration
TARGET_DATABASE = "nyc_taxi_analytics"

# Layer Configuration
BRONZE_SCHEMA = f"{TARGET_DATABASE}.bronze"
SILVER_SCHEMA = f"{TARGET_DATABASE}.silver"
GOLD_SCHEMA = f"{TARGET_DATABASE}.gold"

# Table Names
BRONZE_TABLE = f"{BRONZE_SCHEMA}.trips_raw"
SILVER_TABLE = f"{SILVER_SCHEMA}.trips_cleaned"

# Gold Layer Tables
GOLD_DAILY_SUMMARY = f"{GOLD_SCHEMA}.daily_trip_summary"
GOLD_ZONE_ANALYSIS = f"{GOLD_SCHEMA}.zone_analysis"
GOLD_PAYMENT_ANALYSIS = f"{GOLD_SCHEMA}.payment_analysis"
GOLD_DRIVER_PERFORMANCE = f"{GOLD_SCHEMA}.driver_performance"

# Storage Configuration
BRONZE_PATH = "/mnt/datalake/bronze/nyc_taxi/"
SILVER_PATH = "/mnt/datalake/silver/nyc_taxi/"
GOLD_PATH = "/mnt/datalake/gold/nyc_taxi/"

# Checkpoint Locations
BRONZE_CHECKPOINT = "/mnt/datalake/checkpoints/bronze/nyc_taxi/"
SILVER_CHECKPOINT = "/mnt/datalake/checkpoints/silver/nyc_taxi/"
GOLD_CHECKPOINT = "/mnt/datalake/checkpoints/gold/nyc_taxi/"

# Data Quality Thresholds
MIN_TRIP_DISTANCE = 0.1  # Minimum trip distance in miles
MAX_TRIP_DISTANCE = 100  # Maximum trip distance in miles
MIN_FARE_AMOUNT = 0      # Minimum fare amount
MAX_FARE_AMOUNT = 1000   # Maximum fare amount
MIN_PASSENGER_COUNT = 1  # Minimum passenger count
MAX_PASSENGER_COUNT = 8  # Maximum passenger count

# Processing Configuration
BATCH_SIZE = 10000
PARALLELISM = 8
SHUFFLE_PARTITIONS = 200

# Workflow Configuration
WORKFLOW_TIMEOUT = 3600  # 1 hour in seconds
RETRY_COUNT = 3
RETRY_DELAY = 300  # 5 minutes in seconds

# Notification Configuration
NOTIFICATION_EMAIL = "<EMAIL>"
SLACK_WEBHOOK = "your-slack-webhook-url"

# Data Retention Configuration (in days)
BRONZE_RETENTION_DAYS = 90
SILVER_RETENTION_DAYS = 365
GOLD_RETENTION_DAYS = 1095  # 3 years
