"""
Bronze Layer - Raw Data Ingestion
Ingests raw data from nyctaxi.trips table with minimal transformations
"""

from pyspark.sql import SparkSession
from pyspark.sql.functions import *
from pyspark.sql.types import *
import sys
import os
from datetime import datetime

# Add utils and config to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from utils.common_utils import *
from utils.data_quality import *
import config.config as config

def ingest_raw_trips_data(spark: SparkSession, incremental: bool = True) -> None:
    """
    Ingest raw trips data from source table to Bronze layer
    """
    logger = setup_logging()
    logger.info("Starting Bronze layer data ingestion")
    
    try:
        # Create target database and schema if not exists
        create_database_if_not_exists(spark, config.TARGET_DATABASE)
        spark.sql(f"CREATE SCHEMA IF NOT EXISTS {config.BRONZE_SCHEMA}")
        
        # Read source data
        logger.info(f"Reading data from {config.SOURCE_DATABASE}.{config.SOURCE_TABLE}")
        
        if incremental:
            # Get last ingestion timestamp for incremental load
            last_timestamp = get_max_timestamp(spark, config.BRONZE_TABLE, "ingestion_timestamp")
            logger.info(f"Last ingestion timestamp: {last_timestamp}")
            
            # For incremental load, we'll use a date-based filter since the source might not have ingestion_timestamp
            # Assuming the source table has a date column for filtering
            source_df = spark.table(f"{config.SOURCE_DATABASE}.{config.SOURCE_TABLE}")
            
            # If this is the first run, load all data
            if last_timestamp == "1900-01-01 00:00:00":
                logger.info("First run detected, loading all historical data")
                filtered_df = source_df
            else:
                # For incremental loads, you might want to filter based on pickup date
                # This is a simplified approach - adjust based on your actual requirements
                logger.info("Performing incremental load")
                filtered_df = source_df.filter(
                    col("tpep_pickup_datetime") >= lit(last_timestamp)
                )
        else:
            # Full load
            logger.info("Performing full load")
            source_df = spark.table(f"{config.SOURCE_DATABASE}.{config.SOURCE_TABLE}")
            filtered_df = source_df
        
        # Validate source data
        validate_dataframe_not_empty(filtered_df, "source trips data")
        
        # Add audit columns for Bronze layer
        bronze_df = add_audit_columns(filtered_df)
        
        # Add Bronze-specific metadata
        bronze_df = bronze_df.withColumn("bronze_load_type", lit("incremental" if incremental else "full")) \
                           .withColumn("source_table", lit(f"{config.SOURCE_DATABASE}.{config.SOURCE_TABLE}")) \
                           .withColumn("bronze_version", lit("1.0"))
        
        # Show sample data
        logger.info("Sample of ingested data:")
        bronze_df.show(5, truncate=False)
        
        # Write to Bronze table
        logger.info(f"Writing data to Bronze table: {config.BRONZE_TABLE}")
        
        if incremental and last_timestamp != "1900-01-01 00:00:00":
            # Append mode for incremental loads
            write_delta_table(
                bronze_df, 
                config.BRONZE_TABLE, 
                mode="append",
                partition_cols=["ingestion_date"]
            )
        else:
            # Overwrite mode for first run or full loads
            write_delta_table(
                bronze_df, 
                config.BRONZE_TABLE, 
                mode="overwrite",
                partition_cols=["ingestion_date"]
            )
        
        # Log record counts
        record_count = bronze_df.count()
        logger.info(f"Successfully ingested {record_count:,} records to Bronze layer")
        
        # Optimize table for better performance
        optimize_table(spark, config.BRONZE_TABLE)
        
        # Create or update table properties
        spark.sql(f"""
            ALTER TABLE {config.BRONZE_TABLE} 
            SET TBLPROPERTIES (
                'delta.autoOptimize.optimizeWrite' = 'true',
                'delta.autoOptimize.autoCompact' = 'true',
                'description' = 'Bronze layer table containing raw NYC taxi trip data',
                'layer' = 'bronze',
                'source' = '{config.SOURCE_DATABASE}.{config.SOURCE_TABLE}',
                'last_updated' = '{datetime.now().isoformat()}'
            )
        """)
        
        logger.info("Bronze layer ingestion completed successfully")
        
    except Exception as e:
        logger.error(f"Error in Bronze layer ingestion: {str(e)}")
        raise

def create_bronze_table_schema() -> None:
    """
    Create Bronze table with proper schema if it doesn't exist
    """
    spark = get_spark_session("Bronze_Schema_Creation")
    
    # Define schema based on typical NYC taxi data structure
    # Adjust this schema based on your actual source table structure
    bronze_schema = """
        CREATE TABLE IF NOT EXISTS {bronze_table} (
            VendorID BIGINT,
            tpep_pickup_datetime TIMESTAMP,
            tpep_dropoff_datetime TIMESTAMP,
            passenger_count DOUBLE,
            trip_distance DOUBLE,
            RatecodeID DOUBLE,
            store_and_fwd_flag STRING,
            PULocationID BIGINT,
            DOLocationID BIGINT,
            payment_type BIGINT,
            fare_amount DOUBLE,
            extra DOUBLE,
            mta_tax DOUBLE,
            tip_amount DOUBLE,
            tolls_amount DOUBLE,
            improvement_surcharge DOUBLE,
            total_amount DOUBLE,
            congestion_surcharge DOUBLE,
            airport_fee DOUBLE,
            -- Audit columns
            ingestion_timestamp TIMESTAMP,
            ingestion_date DATE,
            source_system STRING,
            pipeline_run_id STRING,
            bronze_load_type STRING,
            source_table STRING,
            bronze_version STRING
        )
        USING DELTA
        PARTITIONED BY (ingestion_date)
        LOCATION '{bronze_path}'
        TBLPROPERTIES (
            'delta.autoOptimize.optimizeWrite' = 'true',
            'delta.autoOptimize.autoCompact' = 'true'
        )
    """.format(
        bronze_table=config.BRONZE_TABLE,
        bronze_path=config.BRONZE_PATH
    )
    
    spark.sql(bronze_schema)
    print(f"Bronze table {config.BRONZE_TABLE} created successfully")

def main():
    """
    Main function to run Bronze layer ingestion
    """
    spark = get_spark_session("NYC_Taxi_Bronze_Layer")
    
    try:
        # Create Bronze table schema if needed
        create_bronze_table_schema()
        
        # Run incremental ingestion
        ingest_raw_trips_data(spark, incremental=True)
        
    except Exception as e:
        print(f"Bronze layer processing failed: {str(e)}")
        raise
    finally:
        spark.stop()

if __name__ == "__main__":
    main()
