# NYC Taxi Analytics - Deployment Guide

This guide provides step-by-step instructions for deploying the NYC Taxi Analytics pipeline to Azure Databricks.

## Prerequisites

1. **Azure Databricks Workspace**: Ensure you have access to an Azure Databricks workspace
2. **Databricks CLI**: Install and configure the Databricks CLI
3. **Python Environment**: Python 3.8+ with required dependencies
4. **Source Data**: Access to the `nyctaxi.trips` sample database in Databricks

## Installation Steps

### 1. Setup Local Environment

```bash
# Clone or download the project
cd nyc-taxi-analytics

# Install dependencies
pip install -r requirements.txt

# Make scripts executable
chmod +x scripts/run_pipeline.sh
```

### 2. Configure Databricks CLI

```bash
# Install Databricks CLI
pip install databricks-cli

# Configure CLI with your workspace
databricks configure --token

# Test connection
databricks workspace list /
```

### 3. Update Configuration

Edit `config/config.py` to match your environment:

```python
# Update these values based on your setup
SOURCE_DATABASE = "nyctaxi"  # Your source database
SOURCE_TABLE = "trips"       # Your source table
TARGET_DATABASE = "nyc_taxi_analytics"  # Target database name

# Update storage paths if using external storage
BRONZE_PATH = "/mnt/datalake/bronze/nyc_taxi/"
SILVER_PATH = "/mnt/datalake/silver/nyc_taxi/"
GOLD_PATH = "/mnt/datalake/gold/nyc_taxi/"
```

### 4. Deploy to Databricks

```bash
# Deploy all files and create workflow
python scripts/deploy_to_databricks.py
```

This script will:
- Upload all Python files to Databricks workspace
- Create the workflow job
- Set up necessary secrets
- Validate the deployment

### 5. Manual Deployment (Alternative)

If the automated deployment doesn't work, follow these manual steps:

#### Upload Files to Workspace

1. Create workspace folders:
   - `/Workspace/nyc_taxi_analytics/`
   - `/Workspace/nyc_taxi_analytics/bronze/`
   - `/Workspace/nyc_taxi_analytics/silver/`
   - `/Workspace/nyc_taxi_analytics/gold/`
   - `/Workspace/nyc_taxi_analytics/utils/`
   - `/Workspace/nyc_taxi_analytics/config/`

2. Upload files using Databricks UI or CLI:
   ```bash
   databricks workspace import bronze/bronze_ingestion.py /Workspace/nyc_taxi_analytics/bronze/bronze_ingestion.py --language PYTHON
   databricks workspace import silver/silver_transformation.py /Workspace/nyc_taxi_analytics/silver/silver_transformation.py --language PYTHON
   databricks workspace import gold/gold_aggregations.py /Workspace/nyc_taxi_analytics/gold/gold_aggregations.py --language PYTHON
   # ... repeat for all files
   ```

#### Create Databricks Job

1. Go to Databricks workspace → Workflows
2. Click "Create Job"
3. Import the configuration from `workflows/databricks_workflow.json`
4. Update cluster configurations and paths as needed

## Configuration Options

### Cluster Configuration

Recommended cluster settings for each layer:

**Bronze Layer:**
- Node type: `i3.xlarge` or similar
- Workers: 2-3
- Spark version: 13.3.x-scala2.12 or latest

**Silver Layer:**
- Node type: `i3.xlarge` or similar  
- Workers: 3-4
- Spark version: 13.3.x-scala2.12 or latest

**Gold Layer:**
- Node type: `i3.xlarge` or similar
- Workers: 4-5
- Spark version: 13.3.x-scala2.12 or latest

### Storage Configuration

If using external storage (S3, ADLS, etc.):

1. Mount storage to Databricks
2. Update paths in `config/config.py`
3. Ensure proper permissions are set

### Scheduling

The default schedule runs daily at 2 AM UTC. To modify:

1. Edit `workflows/databricks_workflow.json`
2. Update the `schedule.quartz_cron_expression`
3. Redeploy the workflow

## Running the Pipeline

### Manual Execution

```bash
# Run individual layers
./scripts/run_pipeline.sh bronze --incremental
./scripts/run_pipeline.sh silver --incremental  
./scripts/run_pipeline.sh gold

# Run full pipeline
./scripts/run_pipeline.sh full --incremental

# Check status
./scripts/run_pipeline.sh status
```

### Databricks Workflow

1. Go to Databricks workspace → Workflows
2. Find "NYC_Taxi_Analytics_Pipeline"
3. Click "Run now" for manual execution
4. Monitor execution in the Runs tab

## Monitoring and Troubleshooting

### Data Quality Dashboard

Access the data quality dashboard:
1. Go to `/Workspace/nyc_taxi_analytics/notebooks/data_quality_dashboard`
2. Run the notebook to see quality metrics and alerts

### Pipeline Logs

Check pipeline execution logs:
```sql
SELECT * FROM nyc_taxi_analytics.pipeline_execution_log 
ORDER BY start_time DESC
```

### Common Issues

**Issue: Source table not found**
- Verify `nyctaxi.trips` table exists
- Check database and table names in config

**Issue: Permission denied**
- Ensure proper IAM roles and permissions
- Check cluster access to storage locations

**Issue: Out of memory errors**
- Increase cluster size
- Optimize Spark configurations
- Check data volume and partitioning

**Issue: Job fails with import errors**
- Verify all files are uploaded correctly
- Check Python path configurations
- Ensure dependencies are installed

## Performance Optimization

### Spark Configurations

Key configurations for optimal performance:

```python
spark.conf.set("spark.sql.adaptive.enabled", "true")
spark.conf.set("spark.sql.adaptive.coalescePartitions.enabled", "true")
spark.conf.set("spark.sql.adaptive.skewJoin.enabled", "true")
spark.conf.set("spark.sql.execution.arrow.pyspark.enabled", "true")
```

### Table Optimization

Regular maintenance tasks:

```sql
-- Optimize tables
OPTIMIZE nyc_taxi_analytics.bronze.trips_raw;
OPTIMIZE nyc_taxi_analytics.silver.trips_cleaned;

-- Vacuum old files (run weekly)
VACUUM nyc_taxi_analytics.bronze.trips_raw RETAIN 168 HOURS;
VACUUM nyc_taxi_analytics.silver.trips_cleaned RETAIN 168 HOURS;
```

## Security Considerations

1. **Secrets Management**: Use Databricks secrets for sensitive configuration
2. **Access Control**: Implement proper table and workspace permissions
3. **Network Security**: Configure VPC/network settings appropriately
4. **Data Encryption**: Ensure data is encrypted at rest and in transit

## Backup and Recovery

1. **Code Backup**: Store code in version control (Git)
2. **Data Backup**: Implement regular data backups
3. **Configuration Backup**: Export and store workflow configurations
4. **Recovery Testing**: Regularly test recovery procedures

## Support and Maintenance

### Regular Tasks

- Monitor data quality metrics weekly
- Review pipeline performance monthly
- Update dependencies quarterly
- Optimize tables monthly

### Alerting

Set up alerts for:
- Pipeline failures
- Data quality issues
- Performance degradation
- Storage usage

For additional support, refer to the project documentation or contact the data engineering team.
