# Import libraries
from pyspark.sql import SparkSession
from pyspark.sql.functions import *
from pyspark.sql.types import *
from datetime import datetime

# Import configuration
import sys
sys.path.append('/Workspace/simple_version')
import config

# Create target database
spark.sql(f"CREATE DATABASE IF NOT EXISTS {config.TARGET_DATABASE}")
print(f"Database {config.TARGET_DATABASE} created successfully")

# Read source data
print(f"Reading data from {config.SOURCE_DATABASE}.{config.SOURCE_TABLE}")

source_df = spark.table(f"{config.SOURCE_DATABASE}.{config.SOURCE_TABLE}")

# Show schema and sample data
print("Source data schema:")
source_df.printSchema()

print("\nSample data:")
source_df.show(5)

# Select only the required columns
required_columns = [
    'tpep_pickup_datetime',
    'tpep_dropoff_datetime', 
    'trip_distance',
    'fare_amount',
    'pickup_zip',
    'dropoff_zip'
]

bronze_df = source_df.select(*required_columns)

print(f"Selected {len(required_columns)} columns for Bronze layer")
bronze_df.show(5)

# Add audit columns for Bronze layer
bronze_df = bronze_df.withColumn("bronze_ingestion_timestamp", current_timestamp()) \
                     .withColumn("bronze_ingestion_date", current_date()) \
                     .withColumn("source_system", lit("nyctaxi_sample_db")) \
                     .withColumn("pipeline_run_id", lit(datetime.now().strftime("%Y%m%d_%H%M%S")))

print("Added audit columns:")
bronze_df.show(5, truncate=False)

# Check data quality before saving
total_records = bronze_df.count()
null_pickup = bronze_df.filter(col("tpep_pickup_datetime").isNull()).count()
null_dropoff = bronze_df.filter(col("tpep_dropoff_datetime").isNull()).count()
null_distance = bronze_df.filter(col("trip_distance").isNull()).count()
null_fare = bronze_df.filter(col("fare_amount").isNull()).count()

print(f"Data Quality Check:")
print(f"Total records: {total_records:,}")
print(f"Null pickup datetime: {null_pickup:,}")
print(f"Null dropoff datetime: {null_dropoff:,}")
print(f"Null trip distance: {null_distance:,}")
print(f"Null fare amount: {null_fare:,}")

# Write to Bronze Delta table
print(f"Writing data to Bronze table: {config.BRONZE_TABLE}")

bronze_df.write \
    .format("delta") \
    .mode("overwrite") \
    .option("overwriteSchema", "true") \
    .partitionBy("bronze_ingestion_date") \
    .saveAsTable(config.BRONZE_TABLE)

print(f"✅ Bronze layer created successfully with {total_records:,} records")

# Verify the Bronze table
bronze_table = spark.table(config.BRONZE_TABLE)

print(f"Bronze table verification:")
print(f"Record count: {bronze_table.count():,}")
print(f"\nSchema:")
bronze_table.printSchema()

print(f"\nSample records:")
bronze_table.show(5, truncate=False)

# Optimize the Bronze table for better performance
spark.sql(f"OPTIMIZE {config.BRONZE_TABLE}")
print(f"✅ Bronze table optimized")

# Add table properties
spark.sql(f"""
    ALTER TABLE {config.BRONZE_TABLE} 
    SET TBLPROPERTIES (
        'description' = 'Bronze layer - Raw NYC taxi trip data',
        'layer' = 'bronze',
        'source' = '{config.SOURCE_DATABASE}.{config.SOURCE_TABLE}',
        'created_date' = '{datetime.now().isoformat()}'
    )
""")

print(f"✅ Bronze layer ingestion completed successfully!")