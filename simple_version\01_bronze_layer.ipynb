{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Bronze Layer - Raw Data Ingestion\n", "\n", "This notebook ingests raw data from the `nyctaxi.trips` table into the Bronze layer with minimal transformations."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "from pyspark.sql import SparkSession\n", "from pyspark.sql.functions import *\n", "from pyspark.sql.types import *\n", "from datetime import datetime\n", "\n", "# Import configuration\n", "import sys\n", "sys.path.append('/Workspace/simple_version')\n", "import config"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create target catalog and database\n", "spark.sql(f\"CREATE CATALOG IF NOT EXISTS {config.TARGET_CATALOG}\")\n", "spark.sql(f\"USE CATALOG {config.TARGET_CATALOG}\")\n", "spark.sql(f\"CREATE DATABASE IF NOT EXISTS {config.TARGET_DATABASE}\")\n", "print(f\"Catalog {config.TARGET_CATALOG} and Database {config.TARGET_DATABASE} created successfully\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Read source data\n", "print(f\"Reading data from {config.SOURCE_DATABASE}.{config.SOURCE_TABLE}\")\n", "\n", "source_df = spark.table(f\"{config.SOURCE_DATABASE}.{config.SOURCE_TABLE}\")\n", "\n", "# Show schema and sample data\n", "print(\"Source data schema:\")\n", "source_df.printSchema()\n", "\n", "print(\"\\nSample data:\")\n", "source_df.show(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Select only the required columns\n", "required_columns = [\n", "    'tpep_pickup_datetime',\n", "    'tpep_dropoff_datetime', \n", "    'trip_distance',\n", "    'fare_amount',\n", "    'pickup_zip',\n", "    'dropoff_zip'\n", "]\n", "\n", "bronze_df = source_df.select(*required_columns)\n", "\n", "print(f\"Selected {len(required_columns)} columns for Bronze layer\")\n", "bronze_df.show(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Add audit columns for Bronze layer\n", "bronze_df = bronze_df.withColumn(\"bronze_ingestion_timestamp\", current_timestamp()) \\\n", "                     .withColumn(\"bronze_ingestion_date\", current_date()) \\\n", "                     .withColumn(\"source_system\", lit(\"nyctaxi_sample_db\")) \\\n", "                     .withColumn(\"pipeline_run_id\", lit(datetime.now().strftime(\"%Y%m%d_%H%M%S\")))\n", "\n", "print(\"Added audit columns:\")\n", "bronze_df.show(5, truncate=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check data quality before saving\n", "total_records = bronze_df.count()\n", "null_pickup = bronze_df.filter(col(\"tpep_pickup_datetime\").isNull()).count()\n", "null_dropoff = bronze_df.filter(col(\"tpep_dropoff_datetime\").isNull()).count()\n", "null_distance = bronze_df.filter(col(\"trip_distance\").isNull()).count()\n", "null_fare = bronze_df.filter(col(\"fare_amount\").isNull()).count()\n", "\n", "print(f\"Data Quality Check:\")\n", "print(f\"Total records: {total_records:,}\")\n", "print(f\"Null pickup datetime: {null_pickup:,}\")\n", "print(f\"Null dropoff datetime: {null_dropoff:,}\")\n", "print(f\"Null trip distance: {null_distance:,}\")\n", "print(f\"Null fare amount: {null_fare:,}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Write to Bronze Delta table\n", "print(f\"Writing data to Bronze table: {config.BRONZE_TABLE}\")\n", "\n", "bronze_df.write \\\n", "    .format(\"delta\") \\\n", "    .mode(\"overwrite\") \\\n", "    .option(\"overwriteSchema\", \"true\") \\\n", "    .partitionBy(\"bronze_ingestion_date\") \\\n", "    .saveAsTable(config.BRONZE_TABLE)\n", "\n", "print(f\"✅ Bronze layer created successfully with {total_records:,} records\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Verify the Bronze table\n", "bronze_table = spark.table(config.BRONZE_TABLE)\n", "\n", "print(f\"Bronze table verification:\")\n", "print(f\"Record count: {bronze_table.count():,}\")\n", "print(f\"\\nSchema:\")\n", "bronze_table.printSchema()\n", "\n", "print(f\"\\nSample records:\")\n", "bronze_table.show(5, truncate=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Optimize the Bronze table for better performance\n", "spark.sql(f\"OPTIMIZE {config.BRONZE_TABLE}\")\n", "print(f\"✅ Bronze table optimized\")\n", "\n", "# Add table properties\n", "spark.sql(f\"\"\"\n", "    ALTER TABLE {config.BRONZE_TABLE} \n", "    SET TBLPROPERTIES (\n", "        'description' = 'Bronze layer - Raw NYC taxi trip data',\n", "        'layer' = 'bronze',\n", "        'source' = '{config.SOURCE_DATABASE}.{config.SOURCE_TABLE}',\n", "        'created_date' = '{datetime.now().isoformat()}'\n", "    )\n", "\"\"\")\n", "\n", "print(f\"✅ Bronze layer ingestion completed successfully!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}