"""
Data Quality utility functions for NYC Taxi Analytics Pipeline
"""

from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import *
from pyspark.sql.types import *
import sys
import os

# Add config to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'config'))
import config

def validate_trip_data(df: DataFrame) -> DataFrame:
    """
    Apply data quality validations for trip data
    """
    # Add data quality flags
    df_validated = df.withColumn(
        "is_valid_trip_distance",
        when((col("trip_distance") >= config.MIN_TRIP_DISTANCE) & 
             (col("trip_distance") <= config.MAX_TRIP_DISTANCE), True).otherwise(False)
    ).withColumn(
        "is_valid_fare_amount",
        when((col("fare_amount") >= config.MIN_FARE_AMOUNT) & 
             (col("fare_amount") <= config.MAX_FARE_AMOUNT), True).otherwise(False)
    ).withColumn(
        "is_valid_passenger_count",
        when((col("passenger_count") >= config.MIN_PASSENGER_COUNT) & 
             (col("passenger_count") <= config.MAX_PASSENGER_COUNT), True).otherwise(False)
    ).withColumn(
        "is_valid_datetime",
        when((col("tpep_pickup_datetime").isNotNull()) & 
             (col("tpep_dropoff_datetime").isNotNull()) &
             (col("tpep_pickup_datetime") < col("tpep_dropoff_datetime")), True).otherwise(False)
    ).withColumn(
        "is_valid_location",
        when((col("PULocationID").isNotNull()) & 
             (col("DOLocationID").isNotNull()) &
             (col("PULocationID") > 0) & 
             (col("DOLocationID") > 0), True).otherwise(False)
    )
    
    # Add overall data quality flag
    df_validated = df_validated.withColumn(
        "is_valid_record",
        when((col("is_valid_trip_distance")) & 
             (col("is_valid_fare_amount")) & 
             (col("is_valid_passenger_count")) & 
             (col("is_valid_datetime")) & 
             (col("is_valid_location")), True).otherwise(False)
    )
    
    return df_validated

def clean_trip_data(df: DataFrame) -> DataFrame:
    """
    Clean and standardize trip data
    """
    df_cleaned = df.filter(col("is_valid_record") == True) \
        .withColumn("trip_duration_minutes", 
                   (unix_timestamp("tpep_dropoff_datetime") - unix_timestamp("tpep_pickup_datetime")) / 60) \
        .withColumn("trip_speed_mph", 
                   when(col("trip_duration_minutes") > 0, 
                        col("trip_distance") / (col("trip_duration_minutes") / 60)).otherwise(0)) \
        .withColumn("pickup_hour", hour("tpep_pickup_datetime")) \
        .withColumn("pickup_day_of_week", dayofweek("tpep_pickup_datetime")) \
        .withColumn("pickup_month", month("tpep_pickup_datetime")) \
        .withColumn("pickup_year", year("tpep_pickup_datetime")) \
        .withColumn("is_weekend", 
                   when(col("pickup_day_of_week").isin([1, 7]), True).otherwise(False))
    
    return df_cleaned

def remove_duplicates(df: DataFrame, key_columns: list) -> DataFrame:
    """
    Remove duplicate records based on key columns
    """
    # Add row number to identify duplicates
    window_spec = Window.partitionBy(*key_columns).orderBy(desc("ingestion_timestamp"))
    
    df_deduped = df.withColumn("row_num", row_number().over(window_spec)) \
                  .filter(col("row_num") == 1) \
                  .drop("row_num")
    
    return df_deduped

def calculate_data_quality_metrics(df: DataFrame) -> dict:
    """
    Calculate data quality metrics
    """
    total_records = df.count()
    
    if total_records == 0:
        return {"total_records": 0, "quality_score": 0}
    
    quality_metrics = df.agg(
        count("*").alias("total_records"),
        sum(when(col("is_valid_record"), 1).otherwise(0)).alias("valid_records"),
        sum(when(col("is_valid_trip_distance"), 1).otherwise(0)).alias("valid_distance"),
        sum(when(col("is_valid_fare_amount"), 1).otherwise(0)).alias("valid_fare"),
        sum(when(col("is_valid_passenger_count"), 1).otherwise(0)).alias("valid_passenger"),
        sum(when(col("is_valid_datetime"), 1).otherwise(0)).alias("valid_datetime"),
        sum(when(col("is_valid_location"), 1).otherwise(0)).alias("valid_location")
    ).collect()[0]
    
    metrics = {
        "total_records": quality_metrics["total_records"],
        "valid_records": quality_metrics["valid_records"],
        "quality_score": round((quality_metrics["valid_records"] / total_records) * 100, 2),
        "distance_validity": round((quality_metrics["valid_distance"] / total_records) * 100, 2),
        "fare_validity": round((quality_metrics["valid_fare"] / total_records) * 100, 2),
        "passenger_validity": round((quality_metrics["valid_passenger"] / total_records) * 100, 2),
        "datetime_validity": round((quality_metrics["valid_datetime"] / total_records) * 100, 2),
        "location_validity": round((quality_metrics["valid_location"] / total_records) * 100, 2)
    }
    
    return metrics

def log_data_quality_metrics(metrics: dict, layer: str) -> None:
    """
    Log data quality metrics
    """
    print(f"\n=== Data Quality Metrics for {layer} Layer ===")
    print(f"Total Records: {metrics['total_records']:,}")
    print(f"Valid Records: {metrics['valid_records']:,}")
    print(f"Overall Quality Score: {metrics['quality_score']}%")
    print(f"Distance Validity: {metrics['distance_validity']}%")
    print(f"Fare Validity: {metrics['fare_validity']}%")
    print(f"Passenger Count Validity: {metrics['passenger_validity']}%")
    print(f"DateTime Validity: {metrics['datetime_validity']}%")
    print(f"Location Validity: {metrics['location_validity']}%")
    print("=" * 50)

def create_data_quality_summary_table(spark: SparkSession, metrics: dict, layer: str, 
                                     table_name: str) -> None:
    """
    Create a summary table for data quality metrics
    """
    quality_data = [
        (layer, datetime.now(), metrics['total_records'], metrics['valid_records'], 
         metrics['quality_score'], metrics['distance_validity'], metrics['fare_validity'],
         metrics['passenger_validity'], metrics['datetime_validity'], metrics['location_validity'])
    ]
    
    schema = StructType([
        StructField("layer", StringType(), True),
        StructField("run_timestamp", TimestampType(), True),
        StructField("total_records", LongType(), True),
        StructField("valid_records", LongType(), True),
        StructField("quality_score", DoubleType(), True),
        StructField("distance_validity", DoubleType(), True),
        StructField("fare_validity", DoubleType(), True),
        StructField("passenger_validity", DoubleType(), True),
        StructField("datetime_validity", DoubleType(), True),
        StructField("location_validity", DoubleType(), True)
    ])
    
    quality_df = spark.createDataFrame(quality_data, schema)
    
    quality_df.write.format("delta").mode("append").saveAsTable(table_name)
    print(f"Data quality metrics saved to {table_name}")
