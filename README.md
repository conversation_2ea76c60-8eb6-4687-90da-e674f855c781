# NYC Taxi Analytics - Medallion Architecture

This project implements a Medallion Architecture (Bronze, Silver, Gold) for NYC Taxi data analytics using PySpark on Azure Databricks.

## Architecture Overview

```
├── bronze/          # Raw data ingestion layer
├── silver/          # Cleaned and validated data layer  
├── gold/            # Business-ready aggregated data layer
├── workflows/       # Databricks workflow configurations
├── config/          # Configuration files
├── utils/           # Utility functions
└── notebooks/       # Databricks notebooks
```

## Data Flow

1. **Bronze Layer**: Ingests raw data from `nyctaxi.trips` table
2. **Silver Layer**: Cleans, validates, and standardizes the data
3. **Gold Layer**: Creates business-ready aggregated tables for analytics

## Layers Description

### Bronze Layer
- Raw data ingestion from source systems
- Minimal transformations
- Data stored in Delta format with schema evolution
- Includes metadata columns (ingestion timestamp, source file, etc.)

### Silver Layer  
- Data quality checks and validation
- Standardized data types and formats
- Deduplication and error handling
- Business rules application

### Gold Layer
- Aggregated data for business consumption
- Dimensional modeling
- Performance optimized for analytics
- Ready for BI tools and reporting

## Getting Started

1. Configure your Databricks environment
2. Update configuration files in `config/`
3. Run the workflows in sequence: Bronze → Silver → Gold
4. Monitor data quality and pipeline performance

## Technologies Used

- Azure Databricks
- PySpark
- Delta Lake
- Databricks Workflows
