{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Silver Layer - Data Cleaning and Validation\n", "\n", "This notebook cleans and validates data from the Bronze layer, applying business rules and data quality checks."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "from pyspark.sql import SparkSession\n", "from pyspark.sql.functions import *\n", "from pyspark.sql.types import *\n", "from pyspark.sql.window import Window\n", "from datetime import datetime\n", "\n", "# Import configuration\n", "import sys\n", "sys.path.append('/Workspace/simple_version')\n", "import config"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Read data from Bronze layer\n", "print(f\"Reading data from Bronze table: {config.BRONZE_TABLE}\")\n", "\n", "bronze_df = spark.table(config.BRONZE_TABLE)\n", "\n", "print(f\"Bronze data count: {bronze_df.count():,}\")\n", "bronze_df.show(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Apply data quality validations\n", "print(\"Applying data quality validations...\")\n", "\n", "silver_df = bronze_df.withColumn(\n", "    \"is_valid_trip_distance\",\n", "    when((col(\"trip_distance\") >= config.MIN_TRIP_DISTANCE) & \n", "         (col(\"trip_distance\") <= config.MAX_TRIP_DISTANCE), True).otherwise(False)\n", ").withColumn(\n", "    \"is_valid_fare_amount\",\n", "    when((col(\"fare_amount\") >= config.MIN_FARE_AMOUNT) & \n", "         (col(\"fare_amount\") <= config.MAX_FARE_AMOUNT), True).otherwise(False)\n", ").withColumn(\n", "    \"is_valid_datetime\",\n", "    when((col(\"tpep_pickup_datetime\").isNotNull()) & \n", "         (col(\"tpep_dropoff_datetime\").isNotNull()) &\n", "         (col(\"tpep_pickup_datetime\") < col(\"tpep_dropoff_datetime\")), True).otherwise(False)\n", ").withColumn(\n", "    \"is_valid_zip\",\n", "    when((col(\"pickup_zip\").isNotNull()) & \n", "         (col(\"dropoff_zip\").isNotNull()), True).otherwise(False)\n", ")\n", "\n", "# Add overall validity flag\n", "silver_df = silver_df.withColumn(\n", "    \"is_valid_record\",\n", "    when((col(\"is_valid_trip_distance\")) & \n", "         (col(\"is_valid_fare_amount\")) & \n", "         (col(\"is_valid_datetime\")) & \n", "         (col(\"is_valid_zip\")), True).otherwise(False)\n", ")\n", "\n", "print(\"Data quality flags added\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate data quality metrics\n", "total_records = silver_df.count()\n", "valid_records = silver_df.filter(col(\"is_valid_record\") == True).count()\n", "quality_score = (valid_records / total_records) * 100\n", "\n", "print(f\"Data Quality Metrics:\")\n", "print(f\"Total records: {total_records:,}\")\n", "print(f\"Valid records: {valid_records:,}\")\n", "print(f\"Quality score: {quality_score:.2f}%\")\n", "\n", "# Show quality breakdown\n", "quality_breakdown = silver_df.agg(\n", "    sum(when(col(\"is_valid_trip_distance\"), 1).otherwise(0)).alias(\"valid_distance\"),\n", "    sum(when(col(\"is_valid_fare_amount\"), 1).otherwise(0)).alias(\"valid_fare\"),\n", "    sum(when(col(\"is_valid_datetime\"), 1).otherwise(0)).alias(\"valid_datetime\"),\n", "    sum(when(col(\"is_valid_zip\"), 1).otherwise(0)).alias(\"valid_zip\")\n", ").collect()[0]\n", "\n", "print(f\"\\nQuality Breakdown:\")\n", "print(f\"Valid distance: {(quality_breakdown['valid_distance']/total_records)*100:.2f}%\")\n", "print(f\"Valid fare: {(quality_breakdown['valid_fare']/total_records)*100:.2f}%\")\n", "print(f\"Valid datetime: {(quality_breakdown['valid_datetime']/total_records)*100:.2f}%\")\n", "print(f\"Valid ZIP: {(quality_breakdown['valid_zip']/total_records)*100:.2f}%\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Filter to keep only valid records and add derived columns\n", "print(\"Creating cleaned dataset with derived columns...\")\n", "\n", "cleaned_df = silver_df.filter(col(\"is_valid_record\") == True) \\\n", "    .withColumn(\"trip_duration_minutes\", \n", "               (unix_timestamp(\"tpep_dropoff_datetime\") - unix_timestamp(\"tpep_pickup_datetime\")) / 60) \\\n", "    .withColumn(\"trip_speed_mph\", \n", "               when(col(\"trip_duration_minutes\") > 0, \n", "                    col(\"trip_distance\") / (col(\"trip_duration_minutes\") / 60)).otherwise(0)) \\\n", "    .withColumn(\"pickup_hour\", hour(\"tpep_pickup_datetime\")) \\\n", "    .withColumn(\"pickup_day_of_week\", dayofweek(\"tpep_pickup_datetime\")) \\\n", "    .withColumn(\"pickup_month\", month(\"tpep_pickup_datetime\")) \\\n", "    .withColumn(\"pickup_year\", year(\"tpep_pickup_datetime\")) \\\n", "    .withColumn(\"is_weekend\", \n", "               when(col(\"pickup_day_of_week\").isin([1, 7]), True).otherwise(False))\n", "\n", "print(f\"Cleaned dataset has {cleaned_df.count():,} records\")\n", "cleaned_df.show(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Remove duplicates based on key columns\n", "print(\"Removing duplicates...\")\n", "\n", "# Define window for deduplication\n", "window_spec = Window.partitionBy(\n", "    \"tpep_pickup_datetime\", \n", "    \"tpep_dropoff_datetime\", \n", "    \"pickup_zip\", \n", "    \"dropoff_zip\"\n", ").orderBy(desc(\"bronze_ingestion_timestamp\"))\n", "\n", "deduped_df = cleaned_df.withColumn(\"row_num\", row_number().over(window_spec)) \\\n", "                      .filter(col(\"row_num\") == 1) \\\n", "                      .drop(\"row_num\")\n", "\n", "print(f\"After deduplication: {deduped_df.count():,} records\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Add Silver layer audit columns\n", "final_silver_df = deduped_df.withColumn(\"silver_processed_timestamp\", current_timestamp()) \\\n", "                           .withColumn(\"silver_processed_date\", current_date()) \\\n", "                           .withColumn(\"data_quality_score\", lit(quality_score))\n", "\n", "print(\"Added Silver layer audit columns\")\n", "final_silver_df.show(5, truncate=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Write to Silver Delta table\n", "print(f\"Writing data to Silver table: {config.SILVER_TABLE}\")\n", "\n", "final_silver_df.write \\\n", "    .format(\"delta\") \\\n", "    .mode(\"overwrite\") \\\n", "    .option(\"overwriteSchema\", \"true\") \\\n", "    .partitionBy(\"silver_processed_date\") \\\n", "    .saveAsTable(config.SILVER_TABLE)\n", "\n", "final_count = final_silver_df.count()\n", "print(f\"✅ Silver layer created successfully with {final_count:,} records\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Verify the Silver table\n", "silver_table = spark.table(config.SILVER_TABLE)\n", "\n", "print(f\"Silver table verification:\")\n", "print(f\"Record count: {silver_table.count():,}\")\n", "print(f\"\\nSchema:\")\n", "silver_table.printSchema()\n", "\n", "print(f\"\\nSample records:\")\n", "silver_table.show(5, truncate=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Show some basic statistics\n", "print(\"Silver Layer Statistics:\")\n", "\n", "stats = silver_table.agg(\n", "    avg(\"trip_distance\").alias(\"avg_distance\"),\n", "    avg(\"fare_amount\").alias(\"avg_fare\"),\n", "    avg(\"trip_duration_minutes\").alias(\"avg_duration\"),\n", "    avg(\"trip_speed_mph\").alias(\"avg_speed\"),\n", "    countDistinct(\"pickup_zip\").alias(\"unique_pickup_zips\"),\n", "    countDistinct(\"dropoff_zip\").alias(\"unique_dropoff_zips\")\n", ").collect()[0]\n", "\n", "print(f\"Average trip distance: {stats['avg_distance']:.2f} miles\")\n", "print(f\"Average fare amount: ${stats['avg_fare']:.2f}\")\n", "print(f\"Average trip duration: {stats['avg_duration']:.2f} minutes\")\n", "print(f\"Average trip speed: {stats['avg_speed']:.2f} mph\")\n", "print(f\"Unique pickup ZIP codes: {stats['unique_pickup_zips']}\")\n", "print(f\"Unique dropoff ZIP codes: {stats['unique_dropoff_zips']}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Optimize the Silver table\n", "spark.sql(f\"OPTIMIZE {config.SILVER_TABLE}\")\n", "print(f\"✅ Silver table optimized\")\n", "\n", "# Add table properties\n", "spark.sql(f\"\"\"\n", "    ALTER TABLE {config.SILVER_TABLE} \n", "    SET TBLPROPERTIES (\n", "        'description' = 'Silver layer - Cleaned and validated NYC taxi trip data',\n", "        'layer' = 'silver',\n", "        'source' = '{config.BRONZE_TABLE}',\n", "        'data_quality_score' = '{quality_score:.2f}',\n", "        'created_date' = '{datetime.now().isoformat()}'\n", "    )\n", "\"\"\")\n", "\n", "print(f\"✅ Silver layer processing completed successfully!\")\n", "print(f\"Quality Score: {quality_score:.2f}%\")\n", "print(f\"Records processed: {final_count:,}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}