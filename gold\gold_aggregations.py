"""
Gold Layer - Business Analytics and Aggregations
Creates business-ready aggregated tables for analytics and reporting
"""

from pyspark.sql import SparkSession
from pyspark.sql.functions import *
from pyspark.sql.types import *
from pyspark.sql.window import Window
import sys
import os
from datetime import datetime

# Add utils and config to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from utils.common_utils import *
import config.config as config

def create_daily_trip_summary(spark: SparkSession) -> None:
    """
    Create daily trip summary aggregations
    """
    logger = setup_logging()
    logger.info("Creating daily trip summary")
    
    # Read from Silver layer
    silver_df = spark.table(config.SILVER_TABLE).filter(col("is_valid_record") == True)
    
    # Create daily aggregations
    daily_summary = silver_df.groupBy(
        col("pickup_year"),
        col("pickup_month"),
        col("pickup_day_of_week"),
        col("silver_processed_date").alias("trip_date")
    ).agg(
        count("*").alias("total_trips"),
        countDistinct("VendorID").alias("unique_vendors"),
        sum("passenger_count").alias("total_passengers"),
        avg("passenger_count").alias("avg_passengers_per_trip"),
        sum("trip_distance").alias("total_distance"),
        avg("trip_distance").alias("avg_trip_distance"),
        sum("trip_duration_minutes").alias("total_duration_minutes"),
        avg("trip_duration_minutes").alias("avg_trip_duration"),
        avg("trip_speed_mph").alias("avg_trip_speed"),
        sum("fare_amount").alias("total_fare_amount"),
        avg("fare_amount").alias("avg_fare_amount"),
        sum("tip_amount").alias("total_tip_amount"),
        avg("tip_amount").alias("avg_tip_amount"),
        sum("total_amount").alias("total_revenue"),
        avg("total_amount").alias("avg_revenue_per_trip"),
        max("total_amount").alias("max_fare"),
        min("total_amount").alias("min_fare"),
        countDistinct("pickup_location_id").alias("unique_pickup_locations"),
        countDistinct("dropoff_location_id").alias("unique_dropoff_locations")
    ).withColumn("avg_tip_percentage", 
                when(col("avg_fare_amount") > 0, 
                     (col("avg_tip_amount") / col("avg_fare_amount")) * 100).otherwise(0)) \
     .withColumn("revenue_per_mile", 
                when(col("avg_trip_distance") > 0, 
                     col("avg_revenue_per_trip") / col("avg_trip_distance")).otherwise(0)) \
     .withColumn("gold_created_timestamp", current_timestamp())
    
    # Write to Gold table
    write_delta_table(
        daily_summary, 
        config.GOLD_DAILY_SUMMARY, 
        mode="overwrite",
        partition_cols=["pickup_year", "pickup_month"]
    )
    
    logger.info(f"Daily summary created with {daily_summary.count():,} records")

def create_zone_analysis(spark: SparkSession) -> None:
    """
    Create location zone analysis
    """
    logger = setup_logging()
    logger.info("Creating zone analysis")
    
    silver_df = spark.table(config.SILVER_TABLE).filter(col("is_valid_record") == True)
    
    # Pickup zone analysis
    pickup_analysis = silver_df.groupBy("pickup_location_id").agg(
        count("*").alias("pickup_count"),
        avg("trip_distance").alias("avg_trip_distance_from_zone"),
        avg("fare_amount").alias("avg_fare_from_zone"),
        avg("tip_amount").alias("avg_tip_from_zone"),
        sum("total_amount").alias("total_revenue_from_zone")
    ).withColumnRenamed("pickup_location_id", "location_id") \
     .withColumn("zone_type", lit("pickup"))
    
    # Dropoff zone analysis
    dropoff_analysis = silver_df.groupBy("dropoff_location_id").agg(
        count("*").alias("dropoff_count"),
        avg("trip_distance").alias("avg_trip_distance_to_zone"),
        avg("fare_amount").alias("avg_fare_to_zone"),
        avg("tip_amount").alias("avg_tip_to_zone"),
        sum("total_amount").alias("total_revenue_to_zone")
    ).withColumnRenamed("dropoff_location_id", "location_id") \
     .withColumn("zone_type", lit("dropoff"))
    
    # Combine pickup and dropoff analysis
    zone_analysis = pickup_analysis.join(
        dropoff_analysis, 
        on="location_id", 
        how="full_outer"
    ).fillna(0).withColumn("gold_created_timestamp", current_timestamp())
    
    # Write to Gold table
    write_delta_table(
        zone_analysis, 
        config.GOLD_ZONE_ANALYSIS, 
        mode="overwrite"
    )
    
    logger.info(f"Zone analysis created with {zone_analysis.count():,} records")

def create_payment_analysis(spark: SparkSession) -> None:
    """
    Create payment method analysis
    """
    logger = setup_logging()
    logger.info("Creating payment analysis")
    
    silver_df = spark.table(config.SILVER_TABLE).filter(col("is_valid_record") == True)
    
    payment_analysis = silver_df.groupBy(
        "payment_type",
        "payment_type_desc",
        "pickup_year",
        "pickup_month"
    ).agg(
        count("*").alias("trip_count"),
        sum("fare_amount").alias("total_fare"),
        avg("fare_amount").alias("avg_fare"),
        sum("tip_amount").alias("total_tips"),
        avg("tip_amount").alias("avg_tip"),
        sum("total_amount").alias("total_revenue"),
        avg("total_amount").alias("avg_revenue"),
        avg("trip_distance").alias("avg_distance"),
        avg("trip_duration_minutes").alias("avg_duration")
    ).withColumn("tip_percentage", 
                when(col("avg_fare") > 0, (col("avg_tip") / col("avg_fare")) * 100).otherwise(0)) \
     .withColumn("gold_created_timestamp", current_timestamp())
    
    # Add payment method rankings
    window_spec = Window.partitionBy("pickup_year", "pickup_month").orderBy(desc("trip_count"))
    payment_analysis = payment_analysis.withColumn("payment_method_rank", 
                                                  row_number().over(window_spec))
    
    # Write to Gold table
    write_delta_table(
        payment_analysis, 
        config.GOLD_PAYMENT_ANALYSIS, 
        mode="overwrite",
        partition_cols=["pickup_year", "pickup_month"]
    )
    
    logger.info(f"Payment analysis created with {payment_analysis.count():,} records")

def create_driver_performance(spark: SparkSession) -> None:
    """
    Create driver/vendor performance analysis
    """
    logger = setup_logging()
    logger.info("Creating driver performance analysis")
    
    silver_df = spark.table(config.SILVER_TABLE).filter(col("is_valid_record") == True)
    
    driver_performance = silver_df.groupBy(
        "VendorID",
        "vendor_name",
        "pickup_year",
        "pickup_month"
    ).agg(
        count("*").alias("total_trips"),
        sum("trip_distance").alias("total_distance"),
        avg("trip_distance").alias("avg_trip_distance"),
        sum("trip_duration_minutes").alias("total_duration"),
        avg("trip_duration_minutes").alias("avg_trip_duration"),
        avg("trip_speed_mph").alias("avg_speed"),
        sum("fare_amount").alias("total_fare"),
        avg("fare_amount").alias("avg_fare"),
        sum("tip_amount").alias("total_tips"),
        avg("tip_amount").alias("avg_tip"),
        sum("total_amount").alias("total_revenue"),
        avg("total_amount").alias("avg_revenue_per_trip"),
        countDistinct("pickup_location_id").alias("unique_pickup_zones"),
        countDistinct("dropoff_location_id").alias("unique_dropoff_zones")
    ).withColumn("revenue_per_mile", 
                when(col("avg_trip_distance") > 0, 
                     col("avg_revenue_per_trip") / col("avg_trip_distance")).otherwise(0)) \
     .withColumn("trips_per_day", col("total_trips") / 30) \
     .withColumn("gold_created_timestamp", current_timestamp())
    
    # Add vendor rankings
    window_spec = Window.partitionBy("pickup_year", "pickup_month").orderBy(desc("total_revenue"))
    driver_performance = driver_performance.withColumn("vendor_revenue_rank", 
                                                      row_number().over(window_spec))
    
    # Write to Gold table
    write_delta_table(
        driver_performance, 
        config.GOLD_DRIVER_PERFORMANCE, 
        mode="overwrite",
        partition_cols=["pickup_year", "pickup_month"]
    )
    
    logger.info(f"Driver performance analysis created with {driver_performance.count():,} records")

def create_time_based_analysis(spark: SparkSession) -> None:
    """
    Create time-based analysis (hourly, day of week patterns)
    """
    logger = setup_logging()
    logger.info("Creating time-based analysis")
    
    silver_df = spark.table(config.SILVER_TABLE).filter(col("is_valid_record") == True)
    
    time_analysis = silver_df.groupBy(
        "pickup_hour",
        "pickup_day_of_week",
        "pickup_time_of_day",
        "is_weekend",
        "pickup_year",
        "pickup_month"
    ).agg(
        count("*").alias("trip_count"),
        avg("trip_distance").alias("avg_distance"),
        avg("trip_duration_minutes").alias("avg_duration"),
        avg("trip_speed_mph").alias("avg_speed"),
        avg("fare_amount").alias("avg_fare"),
        avg("tip_amount").alias("avg_tip"),
        sum("total_amount").alias("total_revenue"),
        avg("passenger_count").alias("avg_passengers")
    ).withColumn("gold_created_timestamp", current_timestamp())
    
    # Write to Gold table
    time_table = f"{config.GOLD_SCHEMA}.time_based_analysis"
    write_delta_table(
        time_analysis, 
        time_table, 
        mode="overwrite",
        partition_cols=["pickup_year", "pickup_month"]
    )
    
    logger.info(f"Time-based analysis created with {time_analysis.count():,} records")

def create_all_gold_tables(spark: SparkSession) -> None:
    """
    Create all Gold layer tables
    """
    logger = setup_logging()
    logger.info("Starting Gold layer aggregations")
    
    try:
        # Create Gold schema if not exists
        spark.sql(f"CREATE SCHEMA IF NOT EXISTS {config.GOLD_SCHEMA}")
        
        # Create all Gold tables
        create_daily_trip_summary(spark)
        create_zone_analysis(spark)
        create_payment_analysis(spark)
        create_driver_performance(spark)
        create_time_based_analysis(spark)
        
        # Optimize all Gold tables
        gold_tables = [
            config.GOLD_DAILY_SUMMARY,
            config.GOLD_ZONE_ANALYSIS,
            config.GOLD_PAYMENT_ANALYSIS,
            config.GOLD_DRIVER_PERFORMANCE,
            f"{config.GOLD_SCHEMA}.time_based_analysis"
        ]
        
        for table in gold_tables:
            optimize_table(spark, table)
            
            # Update table properties
            spark.sql(f"""
                ALTER TABLE {table} 
                SET TBLPROPERTIES (
                    'delta.autoOptimize.optimizeWrite' = 'true',
                    'delta.autoOptimize.autoCompact' = 'true',
                    'description' = 'Gold layer aggregated table for business analytics',
                    'layer' = 'gold',
                    'source' = '{config.SILVER_TABLE}',
                    'last_updated' = '{datetime.now().isoformat()}'
                )
            """)
        
        logger.info("All Gold layer tables created successfully")
        
    except Exception as e:
        logger.error(f"Error in Gold layer processing: {str(e)}")
        raise

def main():
    """
    Main function to run Gold layer aggregations
    """
    spark = get_spark_session("NYC_Taxi_Gold_Layer")
    
    try:
        create_all_gold_tables(spark)
        
    except Exception as e:
        print(f"Gold layer processing failed: {str(e)}")
        raise
    finally:
        spark.stop()

if __name__ == "__main__":
    main()
