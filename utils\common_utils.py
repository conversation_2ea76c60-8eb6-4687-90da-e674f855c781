"""
Common utility functions for NYC Taxi Analytics Pipeline
"""

from pyspark.sql import SparkSession, DataFrame
from pyspark.sql.functions import *
from pyspark.sql.types import *
from delta.tables import DeltaTable
import logging
from datetime import datetime, timedelta
import sys
import os

# Add config to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'config'))
import config

def get_spark_session(app_name: str = "NYC_Taxi_Analytics") -> SparkSession:
    """
    Create and configure Spark session with optimized settings
    """
    spark = SparkSession.builder \
        .appName(app_name) \
        .config("spark.sql.adaptive.enabled", "true") \
        .config("spark.sql.adaptive.coalescePartitions.enabled", "true") \
        .config("spark.sql.adaptive.skewJoin.enabled", "true") \
        .config("spark.sql.shuffle.partitions", config.SHUFFLE_PARTITIONS) \
        .config("spark.sql.execution.arrow.pyspark.enabled", "true") \
        .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer") \
        .getOrCreate()
    
    spark.conf.set("spark.sql.execution.arrow.maxRecordsPerBatch", config.BATCH_SIZE)
    return spark

def setup_logging(log_level: str = "INFO") -> logging.Logger:
    """
    Setup logging configuration
    """
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)

def add_audit_columns(df: DataFrame) -> DataFrame:
    """
    Add audit columns to DataFrame
    """
    return df.withColumn("ingestion_timestamp", current_timestamp()) \
             .withColumn("ingestion_date", current_date()) \
             .withColumn("source_system", lit("nyctaxi_sample_db")) \
             .withColumn("pipeline_run_id", lit(str(datetime.now().strftime("%Y%m%d_%H%M%S"))))

def validate_dataframe_not_empty(df: DataFrame, table_name: str) -> bool:
    """
    Validate that DataFrame is not empty
    """
    count = df.count()
    if count == 0:
        raise ValueError(f"DataFrame for {table_name} is empty")
    return True

def create_database_if_not_exists(spark: SparkSession, database_name: str) -> None:
    """
    Create database if it doesn't exist
    """
    spark.sql(f"CREATE DATABASE IF NOT EXISTS {database_name}")
    print(f"Database {database_name} created or already exists")

def optimize_table(spark: SparkSession, table_name: str) -> None:
    """
    Optimize Delta table
    """
    try:
        spark.sql(f"OPTIMIZE {table_name}")
        print(f"Table {table_name} optimized successfully")
    except Exception as e:
        print(f"Failed to optimize table {table_name}: {str(e)}")

def vacuum_table(spark: SparkSession, table_name: str, retention_hours: int = 168) -> None:
    """
    Vacuum Delta table to remove old files
    """
    try:
        spark.sql(f"VACUUM {table_name} RETAIN {retention_hours} HOURS")
        print(f"Table {table_name} vacuumed successfully")
    except Exception as e:
        print(f"Failed to vacuum table {table_name}: {str(e)}")

def get_max_timestamp(spark: SparkSession, table_name: str, timestamp_column: str = "ingestion_timestamp") -> str:
    """
    Get maximum timestamp from a table for incremental processing
    """
    try:
        result = spark.sql(f"SELECT MAX({timestamp_column}) as max_ts FROM {table_name}").collect()
        if result[0]['max_ts'] is not None:
            return result[0]['max_ts'].strftime("%Y-%m-%d %H:%M:%S")
        else:
            return "1900-01-01 00:00:00"  # Default for first run
    except Exception as e:
        print(f"Table {table_name} doesn't exist or error occurred: {str(e)}")
        return "1900-01-01 00:00:00"

def write_delta_table(df: DataFrame, table_name: str, mode: str = "append", 
                     partition_cols: list = None, merge_keys: list = None) -> None:
    """
    Write DataFrame to Delta table with various modes
    """
    writer = df.write.format("delta").mode(mode)
    
    if partition_cols:
        writer = writer.partitionBy(*partition_cols)
    
    if mode == "overwrite":
        writer.option("overwriteSchema", "true")
    
    writer.saveAsTable(table_name)
    print(f"Data written to {table_name} successfully in {mode} mode")

def merge_delta_table(source_df: DataFrame, target_table: str, merge_keys: list, 
                     update_columns: list = None) -> None:
    """
    Merge data into Delta table using MERGE operation
    """
    try:
        target_delta = DeltaTable.forName(spark, target_table)
        
        merge_condition = " AND ".join([f"target.{key} = source.{key}" for key in merge_keys])
        
        merge_builder = target_delta.alias("target").merge(
            source_df.alias("source"),
            merge_condition
        )
        
        if update_columns:
            update_dict = {col: f"source.{col}" for col in update_columns}
            merge_builder = merge_builder.whenMatchedUpdate(set=update_dict)
        else:
            merge_builder = merge_builder.whenMatchedUpdateAll()
        
        merge_builder.whenNotMatchedInsertAll().execute()
        
        print(f"Data merged into {target_table} successfully")
        
    except Exception as e:
        print(f"Failed to merge data into {target_table}: {str(e)}")
        raise
