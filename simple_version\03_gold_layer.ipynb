# Import libraries
from pyspark.sql import SparkSession
from pyspark.sql.functions import *
from pyspark.sql.types import *
from pyspark.sql.window import Window
from datetime import datetime

# Import configuration
import sys
sys.path.append('/Workspace/simple_version')
import config

# Set catalog context
spark.sql(f"USE CATALOG {config.TARGET_CATALOG}")

# Read data from Silver layer
print(f"Reading data from Silver table: {config.SILVER_TABLE}")

silver_df = spark.table(config.SILVER_TABLE)

print(f"Silver data count: {silver_df.count():,}")
silver_df.show(5)

# Create daily trip summary
print("Creating daily trip summary...")

daily_summary = silver_df.groupBy(
    col("pickup_year"),
    col("pickup_month"),
    col("pickup_day_of_week"),
    col("silver_processed_date").alias("trip_date")
).agg(
    count("*").alias("total_trips"),
    sum("trip_distance").alias("total_distance"),
    avg("trip_distance").alias("avg_trip_distance"),
    sum("trip_duration_minutes").alias("total_duration_minutes"),
    avg("trip_duration_minutes").alias("avg_trip_duration"),
    avg("trip_speed_mph").alias("avg_trip_speed"),
    sum("fare_amount").alias("total_fare_amount"),
    avg("fare_amount").alias("avg_fare_amount"),
    max("fare_amount").alias("max_fare"),
    min("fare_amount").alias("min_fare"),
    countDistinct("pickup_zip").alias("unique_pickup_zips"),
    countDistinct("dropoff_zip").alias("unique_dropoff_zips")
).withColumn("revenue_per_mile", 
            when(col("avg_trip_distance") > 0, 
                 col("avg_fare_amount") / col("avg_trip_distance")).otherwise(0)) \
 .withColumn("gold_created_timestamp", current_timestamp())

print(f"Daily summary created with {daily_summary.count():,} records")
daily_summary.show(10)

# Save daily summary to Gold table
daily_summary.write \
    .format("delta") \
    .mode("overwrite") \
    .option("overwriteSchema", "true") \
    .partitionBy("pickup_year", "pickup_month") \
    .saveAsTable(config.GOLD_DAILY_SUMMARY)

print(f"✅ Daily summary saved to {config.GOLD_DAILY_SUMMARY}")

# Create ZIP code analysis
print("Creating ZIP code analysis...")

# Pickup ZIP analysis
pickup_analysis = silver_df.groupBy("pickup_zip").agg(
    count("*").alias("pickup_count"),
    avg("trip_distance").alias("avg_trip_distance_from_zip"),
    avg("fare_amount").alias("avg_fare_from_zip"),
    sum("fare_amount").alias("total_revenue_from_zip"),
    avg("trip_duration_minutes").alias("avg_duration_from_zip")
).withColumnRenamed("pickup_zip", "zip_code") \
 .withColumn("analysis_type", lit("pickup"))

# Dropoff ZIP analysis
dropoff_analysis = silver_df.groupBy("dropoff_zip").agg(
    count("*").alias("dropoff_count"),
    avg("trip_distance").alias("avg_trip_distance_to_zip"),
    avg("fare_amount").alias("avg_fare_to_zip"),
    sum("fare_amount").alias("total_revenue_to_zip"),
    avg("trip_duration_minutes").alias("avg_duration_to_zip")
).withColumnRenamed("dropoff_zip", "zip_code") \
 .withColumn("analysis_type", lit("dropoff"))

# Combine pickup and dropoff analysis
zip_analysis = pickup_analysis.join(
    dropoff_analysis, 
    on="zip_code", 
    how="full_outer"
).fillna(0).withColumn("gold_created_timestamp", current_timestamp())

print(f"ZIP analysis created with {zip_analysis.count():,} records")
zip_analysis.show(10)

# Save ZIP analysis to Gold table
zip_analysis.write \
    .format("delta") \
    .mode("overwrite") \
    .option("overwriteSchema", "true") \
    .saveAsTable(config.GOLD_ZIP_ANALYSIS)

print(f"✅ ZIP analysis saved to {config.GOLD_ZIP_ANALYSIS}")

# Create time-based analysis
print("Creating time-based analysis...")

time_analysis = silver_df.groupBy(
    "pickup_hour",
    "pickup_day_of_week",
    "is_weekend",
    "pickup_year",
    "pickup_month"
).agg(
    count("*").alias("trip_count"),
    avg("trip_distance").alias("avg_distance"),
    avg("trip_duration_minutes").alias("avg_duration"),
    avg("trip_speed_mph").alias("avg_speed"),
    avg("fare_amount").alias("avg_fare"),
    sum("fare_amount").alias("total_revenue")
).withColumn("time_period",
            when(col("pickup_hour").between(6, 11), "Morning")
            .when(col("pickup_hour").between(12, 17), "Afternoon")
            .when(col("pickup_hour").between(18, 21), "Evening")
            .otherwise("Night")) \
 .withColumn("day_type", 
            when(col("is_weekend"), "Weekend").otherwise("Weekday")) \
 .withColumn("gold_created_timestamp", current_timestamp())

print(f"Time analysis created with {time_analysis.count():,} records")
time_analysis.show(10)

# Save time analysis to Gold table
time_analysis.write \
    .format("delta") \
    .mode("overwrite") \
    .option("overwriteSchema", "true") \
    .partitionBy("pickup_year", "pickup_month") \
    .saveAsTable(config.GOLD_TIME_ANALYSIS)

print(f"✅ Time analysis saved to {config.GOLD_TIME_ANALYSIS}")

# Generate business insights
print("=== BUSINESS INSIGHTS ===")

# Top 10 pickup ZIP codes by trip count
print("\nTop 10 Pickup ZIP Codes by Trip Count:")
top_pickup_zips = spark.table(config.GOLD_ZIP_ANALYSIS) \
    .select("zip_code", "pickup_count") \
    .filter(col("pickup_count") > 0) \
    .orderBy(desc("pickup_count")) \
    .limit(10)
top_pickup_zips.show()

# Top 10 dropoff ZIP codes by trip count
print("\nTop 10 Dropoff ZIP Codes by Trip Count:")
top_dropoff_zips = spark.table(config.GOLD_ZIP_ANALYSIS) \
    .select("zip_code", "dropoff_count") \
    .filter(col("dropoff_count") > 0) \
    .orderBy(desc("dropoff_count")) \
    .limit(10)
top_dropoff_zips.show()

# Peak hours analysis
print("\nPeak Hours Analysis:")
peak_hours = spark.table(config.GOLD_TIME_ANALYSIS) \
    .groupBy("pickup_hour") \
    .agg(sum("trip_count").alias("total_trips")) \
    .orderBy(desc("total_trips")) \
    .limit(10)
peak_hours.show()

# Weekend vs Weekday comparison
print("\nWeekend vs Weekday Comparison:")
day_comparison = spark.table(config.GOLD_TIME_ANALYSIS) \
    .groupBy("day_type") \
    .agg(
        sum("trip_count").alias("total_trips"),
        avg("avg_fare").alias("avg_fare"),
        avg("avg_distance").alias("avg_distance"),
        sum("total_revenue").alias("total_revenue")
    )
day_comparison.show()

# Overall summary statistics
print("\n=== OVERALL SUMMARY STATISTICS ===")

# Get summary from daily summary table
overall_stats = spark.table(config.GOLD_DAILY_SUMMARY).agg(
    sum("total_trips").alias("grand_total_trips"),
    sum("total_distance").alias("grand_total_distance"),
    avg("avg_trip_distance").alias("overall_avg_distance"),
    sum("total_fare_amount").alias("grand_total_revenue"),
    avg("avg_fare_amount").alias("overall_avg_fare"),
    avg("avg_trip_speed").alias("overall_avg_speed")
).collect()[0]

print(f"Total Trips: {overall_stats['grand_total_trips']:,}")
print(f"Total Distance: {overall_stats['grand_total_distance']:,.2f} miles")
print(f"Average Trip Distance: {overall_stats['overall_avg_distance']:.2f} miles")
print(f"Total Revenue: ${overall_stats['grand_total_revenue']:,.2f}")
print(f"Average Fare: ${overall_stats['overall_avg_fare']:.2f}")
print(f"Average Speed: {overall_stats['overall_avg_speed']:.2f} mph")

# Optimize all Gold tables
gold_tables = [
    config.GOLD_DAILY_SUMMARY,
    config.GOLD_ZIP_ANALYSIS,
    config.GOLD_TIME_ANALYSIS
]

for table in gold_tables:
    print(f"Optimizing {table}...")
    spark.sql(f"OPTIMIZE {table}")
    
    # Add table properties
    spark.sql(f"""
        ALTER TABLE {table} 
        SET TBLPROPERTIES (
            'description' = 'Gold layer aggregated table for business analytics',
            'layer' = 'gold',
            'source' = '{config.SILVER_TABLE}',
            'created_date' = '{datetime.now().isoformat()}'
        )
    """)

print(f"\n✅ All Gold layer tables optimized and configured!")

# Final summary
print("\n" + "="*60)
print("🎉 GOLD LAYER PROCESSING COMPLETED SUCCESSFULLY!")
print("="*60)

print(f"\n📊 Gold Tables Created:")
for table in gold_tables:
    count = spark.table(table).count()
    print(f"  ✓ {table}: {count:,} records")

print(f"\n🏆 Key Metrics:")
print(f"  • Total Trips Analyzed: {overall_stats['grand_total_trips']:,}")
print(f"  • Total Revenue: ${overall_stats['grand_total_revenue']:,.2f}")
print(f"  • Average Fare per Trip: ${overall_stats['overall_avg_fare']:.2f}")

print(f"\n📈 Ready for Business Intelligence and Reporting!")
print("="*60)