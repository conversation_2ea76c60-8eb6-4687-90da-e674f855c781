{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Gold Layer - Business Analytics and Aggregations\n", "\n", "This notebook creates business-ready aggregated tables for analytics and reporting from the Silver layer data."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "from pyspark.sql import SparkSession\n", "from pyspark.sql.functions import *\n", "from pyspark.sql.types import *\n", "from pyspark.sql.window import Window\n", "from datetime import datetime\n", "\n", "# Import configuration\n", "import sys\n", "sys.path.append('/Workspace/simple_version')\n", "import config"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Read data from Silver layer\n", "print(f\"Reading data from Silver table: {config.SILVER_TABLE}\")\n", "\n", "silver_df = spark.table(config.SILVER_TABLE)\n", "\n", "print(f\"Silver data count: {silver_df.count():,}\")\n", "silver_df.show(5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Daily Trip Summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create daily trip summary\n", "print(\"Creating daily trip summary...\")\n", "\n", "daily_summary = silver_df.groupBy(\n", "    col(\"pickup_year\"),\n", "    col(\"pickup_month\"),\n", "    col(\"pickup_day_of_week\"),\n", "    col(\"silver_processed_date\").alias(\"trip_date\")\n", ").agg(\n", "    count(\"*\").alias(\"total_trips\"),\n", "    sum(\"trip_distance\").alias(\"total_distance\"),\n", "    avg(\"trip_distance\").alias(\"avg_trip_distance\"),\n", "    sum(\"trip_duration_minutes\").alias(\"total_duration_minutes\"),\n", "    avg(\"trip_duration_minutes\").alias(\"avg_trip_duration\"),\n", "    avg(\"trip_speed_mph\").alias(\"avg_trip_speed\"),\n", "    sum(\"fare_amount\").alias(\"total_fare_amount\"),\n", "    avg(\"fare_amount\").alias(\"avg_fare_amount\"),\n", "    max(\"fare_amount\").alias(\"max_fare\"),\n", "    min(\"fare_amount\").alias(\"min_fare\"),\n", "    countDistinct(\"pickup_zip\").alias(\"unique_pickup_zips\"),\n", "    countDistinct(\"dropoff_zip\").alias(\"unique_dropoff_zips\")\n", ").withColumn(\"revenue_per_mile\", \n", "            when(col(\"avg_trip_distance\") > 0, \n", "                 col(\"avg_fare_amount\") / col(\"avg_trip_distance\")).otherwise(0)) \\\n", " .withColumn(\"gold_created_timestamp\", current_timestamp())\n", "\n", "print(f\"Daily summary created with {daily_summary.count():,} records\")\n", "daily_summary.show(10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save daily summary to Gold table\n", "daily_summary.write \\\n", "    .format(\"delta\") \\\n", "    .mode(\"overwrite\") \\\n", "    .option(\"overwriteSchema\", \"true\") \\\n", "    .partitionBy(\"pickup_year\", \"pickup_month\") \\\n", "    .saveAsTable(config.GOLD_DAILY_SUMMARY)\n", "\n", "print(f\"✅ Daily summary saved to {config.GOLD_DAILY_SUMMARY}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. ZIP Code Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create ZIP code analysis\n", "print(\"Creating ZIP code analysis...\")\n", "\n", "# Pickup ZIP analysis\n", "pickup_analysis = silver_df.groupBy(\"pickup_zip\").agg(\n", "    count(\"*\").alias(\"pickup_count\"),\n", "    avg(\"trip_distance\").alias(\"avg_trip_distance_from_zip\"),\n", "    avg(\"fare_amount\").alias(\"avg_fare_from_zip\"),\n", "    sum(\"fare_amount\").alias(\"total_revenue_from_zip\"),\n", "    avg(\"trip_duration_minutes\").alias(\"avg_duration_from_zip\")\n", ").withColumnRenamed(\"pickup_zip\", \"zip_code\") \\\n", " .withColumn(\"analysis_type\", lit(\"pickup\"))\n", "\n", "# Dropoff ZIP analysis\n", "dropoff_analysis = silver_df.groupBy(\"dropoff_zip\").agg(\n", "    count(\"*\").alias(\"dropoff_count\"),\n", "    avg(\"trip_distance\").alias(\"avg_trip_distance_to_zip\"),\n", "    avg(\"fare_amount\").alias(\"avg_fare_to_zip\"),\n", "    sum(\"fare_amount\").alias(\"total_revenue_to_zip\"),\n", "    avg(\"trip_duration_minutes\").alias(\"avg_duration_to_zip\")\n", ").withColumnRenamed(\"dropoff_zip\", \"zip_code\") \\\n", " .withColumn(\"analysis_type\", lit(\"dropoff\"))\n", "\n", "# Combine pickup and dropoff analysis\n", "zip_analysis = pickup_analysis.join(\n", "    dropoff_analysis, \n", "    on=\"zip_code\", \n", "    how=\"full_outer\"\n", ").fillna(0).withColumn(\"gold_created_timestamp\", current_timestamp())\n", "\n", "print(f\"ZIP analysis created with {zip_analysis.count():,} records\")\n", "zip_analysis.show(10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save ZIP analysis to Gold table\n", "zip_analysis.write \\\n", "    .format(\"delta\") \\\n", "    .mode(\"overwrite\") \\\n", "    .option(\"overwriteSchema\", \"true\") \\\n", "    .saveAsTable(config.GOLD_ZIP_ANALYSIS)\n", "\n", "print(f\"✅ ZIP analysis saved to {config.GOLD_ZIP_ANALYSIS}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Time-based Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create time-based analysis\n", "print(\"Creating time-based analysis...\")\n", "\n", "time_analysis = silver_df.groupBy(\n", "    \"pickup_hour\",\n", "    \"pickup_day_of_week\",\n", "    \"is_weekend\",\n", "    \"pickup_year\",\n", "    \"pickup_month\"\n", ").agg(\n", "    count(\"*\").alias(\"trip_count\"),\n", "    avg(\"trip_distance\").alias(\"avg_distance\"),\n", "    avg(\"trip_duration_minutes\").alias(\"avg_duration\"),\n", "    avg(\"trip_speed_mph\").alias(\"avg_speed\"),\n", "    avg(\"fare_amount\").alias(\"avg_fare\"),\n", "    sum(\"fare_amount\").alias(\"total_revenue\")\n", ").withColumn(\"time_period\",\n", "            when(col(\"pickup_hour\").between(6, 11), \"Morning\")\n", "            .when(col(\"pickup_hour\").between(12, 17), \"Afternoon\")\n", "            .when(col(\"pickup_hour\").between(18, 21), \"Evening\")\n", "            .otherwise(\"Night\")) \\\n", " .withColumn(\"day_type\", \n", "            when(col(\"is_weekend\"), \"Weekend\").otherwise(\"Weekday\")) \\\n", " .withColumn(\"gold_created_timestamp\", current_timestamp())\n", "\n", "print(f\"Time analysis created with {time_analysis.count():,} records\")\n", "time_analysis.show(10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save time analysis to Gold table\n", "time_analysis.write \\\n", "    .format(\"delta\") \\\n", "    .mode(\"overwrite\") \\\n", "    .option(\"overwriteSchema\", \"true\") \\\n", "    .partitionBy(\"pickup_year\", \"pickup_month\") \\\n", "    .saveAsTable(config.GOLD_TIME_ANALYSIS)\n", "\n", "print(f\"✅ Time analysis saved to {config.GOLD_TIME_ANALYSIS}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Business Insights and Summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate business insights\n", "print(\"=== BUSINESS INSIGHTS ===\")\n", "\n", "# Top 10 pickup ZIP codes by trip count\n", "print(\"\\nTop 10 Pickup ZIP Codes by Trip Count:\")\n", "top_pickup_zips = spark.table(config.GOLD_ZIP_ANALYSIS) \\\n", "    .select(\"zip_code\", \"pickup_count\") \\\n", "    .filter(col(\"pickup_count\") > 0) \\\n", "    .orderBy(desc(\"pickup_count\")) \\\n", "    .limit(10)\n", "top_pickup_zips.show()\n", "\n", "# Top 10 dropoff ZIP codes by trip count\n", "print(\"\\nTop 10 Dropoff ZIP Codes by Trip Count:\")\n", "top_dropoff_zips = spark.table(config.GOLD_ZIP_ANALYSIS) \\\n", "    .select(\"zip_code\", \"dropoff_count\") \\\n", "    .filter(col(\"dropoff_count\") > 0) \\\n", "    .orderBy(desc(\"dropoff_count\")) \\\n", "    .limit(10)\n", "top_dropoff_zips.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Peak hours analysis\n", "print(\"\\nPeak Hours Analysis:\")\n", "peak_hours = spark.table(config.GOLD_TIME_ANALYSIS) \\\n", "    .groupBy(\"pickup_hour\") \\\n", "    .agg(sum(\"trip_count\").alias(\"total_trips\")) \\\n", "    .orderBy(desc(\"total_trips\")) \\\n", "    .limit(10)\n", "peak_hours.show()\n", "\n", "# Weekend vs Weekday comparison\n", "print(\"\\nWeekend vs Weekday Comparison:\")\n", "day_comparison = spark.table(config.GOLD_TIME_ANALYSIS) \\\n", "    .groupBy(\"day_type\") \\\n", "    .agg(\n", "        sum(\"trip_count\").alias(\"total_trips\"),\n", "        avg(\"avg_fare\").alias(\"avg_fare\"),\n", "        avg(\"avg_distance\").alias(\"avg_distance\"),\n", "        sum(\"total_revenue\").alias(\"total_revenue\")\n", "    )\n", "day_comparison.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Overall summary statistics\n", "print(\"\\n=== OVERALL SUMMARY STATISTICS ===\")\n", "\n", "# Get summary from daily summary table\n", "overall_stats = spark.table(config.GOLD_DAILY_SUMMARY).agg(\n", "    sum(\"total_trips\").alias(\"grand_total_trips\"),\n", "    sum(\"total_distance\").alias(\"grand_total_distance\"),\n", "    avg(\"avg_trip_distance\").alias(\"overall_avg_distance\"),\n", "    sum(\"total_fare_amount\").alias(\"grand_total_revenue\"),\n", "    avg(\"avg_fare_amount\").alias(\"overall_avg_fare\"),\n", "    avg(\"avg_trip_speed\").alias(\"overall_avg_speed\")\n", ").collect()[0]\n", "\n", "print(f\"Total Trips: {overall_stats['grand_total_trips']:,}\")\n", "print(f\"Total Distance: {overall_stats['grand_total_distance']:,.2f} miles\")\n", "print(f\"Average Trip Distance: {overall_stats['overall_avg_distance']:.2f} miles\")\n", "print(f\"Total Revenue: ${overall_stats['grand_total_revenue']:,.2f}\")\n", "print(f\"Average Fare: ${overall_stats['overall_avg_fare']:.2f}\")\n", "print(f\"Average Speed: {overall_stats['overall_avg_speed']:.2f} mph\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Optimize all Gold tables\n", "gold_tables = [\n", "    config.GOLD_DAILY_SUMMARY,\n", "    config.GOLD_ZIP_ANALYSIS,\n", "    config.GOLD_TIME_ANALYSIS\n", "]\n", "\n", "for table in gold_tables:\n", "    print(f\"Optimizing {table}...\")\n", "    spark.sql(f\"OPTIMIZE {table}\")\n", "    \n", "    # Add table properties\n", "    spark.sql(f\"\"\"\n", "        ALTER TABLE {table} \n", "        SET TBLPROPERTIES (\n", "            'description' = 'Gold layer aggregated table for business analytics',\n", "            'layer' = 'gold',\n", "            'source' = '{config.SILVER_TABLE}',\n", "            'created_date' = '{datetime.now().isoformat()}'\n", "        )\n", "    \"\"\")\n", "\n", "print(f\"\\n✅ All Gold layer tables optimized and configured!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final summary\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"🎉 GOLD LAYER PROCESSING COMPLETED SUCCESSFULLY!\")\n", "print(\"=\"*60)\n", "\n", "print(f\"\\n📊 Gold Tables Created:\")\n", "for table in gold_tables:\n", "    count = spark.table(table).count()\n", "    print(f\"  ✓ {table}: {count:,} records\")\n", "\n", "print(f\"\\n🏆 Key Metrics:\")\n", "print(f\"  • Total Trips Analyzed: {overall_stats['grand_total_trips']:,}\")\n", "print(f\"  • Total Revenue: ${overall_stats['grand_total_revenue']:,.2f}\")\n", "print(f\"  • Average Fare per Trip: ${overall_stats['overall_avg_fare']:.2f}\")\n", "\n", "print(f\"\\n📈 Ready for Business Intelligence and Reporting!\")\n", "print(\"=\"*60)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}