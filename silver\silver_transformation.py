"""
Silver Layer - Data Cleaning and Transformation
Cleans, validates, and standardizes data from Bronze layer
"""

from pyspark.sql import SparkSession
from pyspark.sql.functions import *
from pyspark.sql.types import *
from pyspark.sql.window import Window
import sys
import os
from datetime import datetime

# Add utils and config to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from utils.common_utils import *
from utils.data_quality import *
import config.config as config

def transform_bronze_to_silver(spark: SparkSession, incremental: bool = True) -> None:
    """
    Transform Bronze layer data to Silver layer with cleaning and validation
    """
    logger = setup_logging()
    logger.info("Starting Silver layer transformation")
    
    try:
        # Create target schema if not exists
        spark.sql(f"CREATE SCHEMA IF NOT EXISTS {config.SILVER_SCHEMA}")
        
        # Read from Bronze layer
        logger.info(f"Reading data from Bronze table: {config.BRONZE_TABLE}")
        
        if incremental:
            # Get last processed timestamp for incremental load
            last_timestamp = get_max_timestamp(spark, config.SILVER_TABLE, "silver_processed_timestamp")
            logger.info(f"Last Silver processing timestamp: {last_timestamp}")
            
            if last_timestamp == "1900-01-01 00:00:00":
                # First run - process all Bronze data
                bronze_df = spark.table(config.BRONZE_TABLE)
            else:
                # Incremental - process only new Bronze records
                bronze_df = spark.table(config.BRONZE_TABLE).filter(
                    col("ingestion_timestamp") > lit(last_timestamp)
                )
        else:
            # Full load
            bronze_df = spark.table(config.BRONZE_TABLE)
        
        # Validate source data
        validate_dataframe_not_empty(bronze_df, "Bronze layer data")
        
        logger.info(f"Processing {bronze_df.count():,} records from Bronze layer")
        
        # Apply data quality validations
        logger.info("Applying data quality validations")
        validated_df = validate_trip_data(bronze_df)
        
        # Calculate and log data quality metrics
        quality_metrics = calculate_data_quality_metrics(validated_df)
        log_data_quality_metrics(quality_metrics, "Silver")
        
        # Clean and standardize data
        logger.info("Cleaning and standardizing data")
        cleaned_df = clean_trip_data(validated_df)
        
        # Remove duplicates based on key columns
        logger.info("Removing duplicates")
        deduped_df = remove_duplicates(
            cleaned_df, 
            ["VendorID", "tpep_pickup_datetime", "tpep_dropoff_datetime", "PULocationID", "DOLocationID"]
        )
        
        # Add Silver layer specific columns
        silver_df = deduped_df.withColumn("silver_processed_timestamp", current_timestamp()) \
                             .withColumn("silver_processed_date", current_date()) \
                             .withColumn("silver_version", lit("1.0")) \
                             .withColumn("data_quality_score", 
                                       when(col("is_valid_record"), 100.0).otherwise(0.0))
        
        # Add business-friendly column names and derived fields
        silver_df = silver_df.withColumn("pickup_datetime", col("tpep_pickup_datetime")) \
                           .withColumn("dropoff_datetime", col("tpep_dropoff_datetime")) \
                           .withColumn("pickup_location_id", col("PULocationID")) \
                           .withColumn("dropoff_location_id", col("DOLocationID")) \
                           .withColumn("vendor_name", 
                                     when(col("VendorID") == 1, "Creative Mobile Technologies") \
                                     .when(col("VendorID") == 2, "VeriFone Inc.") \
                                     .otherwise("Unknown")) \
                           .withColumn("payment_type_desc",
                                     when(col("payment_type") == 1, "Credit card") \
                                     .when(col("payment_type") == 2, "Cash") \
                                     .when(col("payment_type") == 3, "No charge") \
                                     .when(col("payment_type") == 4, "Dispute") \
                                     .when(col("payment_type") == 5, "Unknown") \
                                     .when(col("payment_type") == 6, "Voided trip") \
                                     .otherwise("Other")) \
                           .withColumn("rate_code_desc",
                                     when(col("RatecodeID") == 1, "Standard rate") \
                                     .when(col("RatecodeID") == 2, "JFK") \
                                     .when(col("RatecodeID") == 3, "Newark") \
                                     .when(col("RatecodeID") == 4, "Nassau or Westchester") \
                                     .when(col("RatecodeID") == 5, "Negotiated fare") \
                                     .when(col("RatecodeID") == 6, "Group ride") \
                                     .otherwise("Unknown"))
        
        # Add time-based dimensions for analytics
        silver_df = silver_df.withColumn("pickup_time_of_day",
                                       when(col("pickup_hour").between(6, 11), "Morning") \
                                       .when(col("pickup_hour").between(12, 17), "Afternoon") \
                                       .when(col("pickup_hour").between(18, 21), "Evening") \
                                       .otherwise("Night")) \
                              .withColumn("trip_category",
                                        when(col("trip_distance") <= 1, "Short") \
                                        .when(col("trip_distance").between(1, 5), "Medium") \
                                        .when(col("trip_distance").between(5, 15), "Long") \
                                        .otherwise("Very Long"))
        
        # Select final columns for Silver table
        final_columns = [
            # Original columns
            "VendorID", "vendor_name", "pickup_datetime", "dropoff_datetime", 
            "passenger_count", "trip_distance", "RatecodeID", "rate_code_desc",
            "store_and_fwd_flag", "pickup_location_id", "dropoff_location_id",
            "payment_type", "payment_type_desc", "fare_amount", "extra", "mta_tax",
            "tip_amount", "tolls_amount", "improvement_surcharge", "total_amount",
            "congestion_surcharge", "airport_fee",
            
            # Derived columns
            "trip_duration_minutes", "trip_speed_mph", "pickup_hour", 
            "pickup_day_of_week", "pickup_month", "pickup_year", "is_weekend",
            "pickup_time_of_day", "trip_category",
            
            # Data quality columns
            "is_valid_record", "data_quality_score",
            "is_valid_trip_distance", "is_valid_fare_amount", "is_valid_passenger_count",
            "is_valid_datetime", "is_valid_location",
            
            # Audit columns
            "ingestion_timestamp", "ingestion_date", "source_system", "pipeline_run_id",
            "silver_processed_timestamp", "silver_processed_date", "silver_version"
        ]
        
        silver_final_df = silver_df.select(*final_columns)
        
        # Show sample data
        logger.info("Sample of transformed Silver data:")
        silver_final_df.show(5, truncate=False)
        
        # Write to Silver table
        logger.info(f"Writing data to Silver table: {config.SILVER_TABLE}")
        
        if incremental and last_timestamp != "1900-01-01 00:00:00":
            # Append mode for incremental loads
            write_delta_table(
                silver_final_df, 
                config.SILVER_TABLE, 
                mode="append",
                partition_cols=["silver_processed_date"]
            )
        else:
            # Overwrite mode for first run or full loads
            write_delta_table(
                silver_final_df, 
                config.SILVER_TABLE, 
                mode="overwrite",
                partition_cols=["silver_processed_date"]
            )
        
        # Log record counts
        final_record_count = silver_final_df.count()
        logger.info(f"Successfully processed {final_record_count:,} records to Silver layer")
        
        # Optimize table
        optimize_table(spark, config.SILVER_TABLE)
        
        # Update table properties
        spark.sql(f"""
            ALTER TABLE {config.SILVER_TABLE} 
            SET TBLPROPERTIES (
                'delta.autoOptimize.optimizeWrite' = 'true',
                'delta.autoOptimize.autoCompact' = 'true',
                'description' = 'Silver layer table containing cleaned and validated NYC taxi trip data',
                'layer' = 'silver',
                'source' = '{config.BRONZE_TABLE}',
                'last_updated' = '{datetime.now().isoformat()}',
                'data_quality_score' = '{quality_metrics["quality_score"]}'
            )
        """)
        
        # Save data quality metrics
        create_data_quality_summary_table(
            spark, quality_metrics, "Silver", 
            f"{config.SILVER_SCHEMA}.data_quality_metrics"
        )
        
        logger.info("Silver layer transformation completed successfully")
        
    except Exception as e:
        logger.error(f"Error in Silver layer transformation: {str(e)}")
        raise

def create_silver_table_schema() -> None:
    """
    Create Silver table with proper schema if it doesn't exist
    """
    spark = get_spark_session("Silver_Schema_Creation")
    
    silver_schema = f"""
        CREATE TABLE IF NOT EXISTS {config.SILVER_TABLE} (
            VendorID BIGINT,
            vendor_name STRING,
            pickup_datetime TIMESTAMP,
            dropoff_datetime TIMESTAMP,
            passenger_count DOUBLE,
            trip_distance DOUBLE,
            RatecodeID DOUBLE,
            rate_code_desc STRING,
            store_and_fwd_flag STRING,
            pickup_location_id BIGINT,
            dropoff_location_id BIGINT,
            payment_type BIGINT,
            payment_type_desc STRING,
            fare_amount DOUBLE,
            extra DOUBLE,
            mta_tax DOUBLE,
            tip_amount DOUBLE,
            tolls_amount DOUBLE,
            improvement_surcharge DOUBLE,
            total_amount DOUBLE,
            congestion_surcharge DOUBLE,
            airport_fee DOUBLE,
            trip_duration_minutes DOUBLE,
            trip_speed_mph DOUBLE,
            pickup_hour INT,
            pickup_day_of_week INT,
            pickup_month INT,
            pickup_year INT,
            is_weekend BOOLEAN,
            pickup_time_of_day STRING,
            trip_category STRING,
            is_valid_record BOOLEAN,
            data_quality_score DOUBLE,
            is_valid_trip_distance BOOLEAN,
            is_valid_fare_amount BOOLEAN,
            is_valid_passenger_count BOOLEAN,
            is_valid_datetime BOOLEAN,
            is_valid_location BOOLEAN,
            ingestion_timestamp TIMESTAMP,
            ingestion_date DATE,
            source_system STRING,
            pipeline_run_id STRING,
            silver_processed_timestamp TIMESTAMP,
            silver_processed_date DATE,
            silver_version STRING
        )
        USING DELTA
        PARTITIONED BY (silver_processed_date)
        LOCATION '{config.SILVER_PATH}'
        TBLPROPERTIES (
            'delta.autoOptimize.optimizeWrite' = 'true',
            'delta.autoOptimize.autoCompact' = 'true'
        )
    """
    
    spark.sql(silver_schema)
    print(f"Silver table {config.SILVER_TABLE} created successfully")

def main():
    """
    Main function to run Silver layer transformation
    """
    spark = get_spark_session("NYC_Taxi_Silver_Layer")
    
    try:
        # Create Silver table schema if needed
        create_silver_table_schema()
        
        # Run incremental transformation
        transform_bronze_to_silver(spark, incremental=True)
        
    except Exception as e:
        print(f"Silver layer processing failed: {str(e)}")
        raise
    finally:
        spark.stop()

if __name__ == "__main__":
    main()
